import 'package:go_router/go_router.dart';
import 'package:somayya_academy/app/modules/home/<USER>/home_view.dart';

import '../modules/auth/bindings/auth_binding.dart';
import '../modules/auth/views/enter_email_view.dart';
import '../modules/auth/views/login_view.dart';
import '../modules/auth/views/register_email_view.dart';
import '../modules/auth/views/register_password_view.dart';
import '../modules/auth/views/register_profile_view.dart';
import '../modules/auth/views/register_verify_otp_view.dart';
import '../modules/auth/views/reset_password_view.dart';
import '../modules/auth/views/verify_otp_view.dart';

class AppPages {
  static final router = GoRouter(
    routes: [
      GoRoute(
        path: '/login',
        builder: (context, state) {
          AuthBinding().dependencies();
          return LoginView();
        },
      ),
      GoRoute(
        path: '/register',
        builder: (context, state) {
          AuthBinding().dependencies();
          return const RegisterEmailView();
        },
      ),
      GoRoute(
        path: '/register/verify',
        builder: (context, state) {
          final email = state.extra as String;
          AuthBinding().dependencies();
          return RegisterVerifyOtpView(email: email);
        },
      ),
      GoRoute(
        path: '/register/password',
        builder: (context, state) {
          final map = state.extra as Map<String, String>;
          AuthBinding().dependencies();
          return RegisterPasswordView(email: map['email']!, otp: map['otp']!);
        },
      ),
      GoRoute(
        path: '/register/profile',
        builder: (context, state) {
          AuthBinding().dependencies();
          return const RegisterProfileView();
        },
      ),
      GoRoute(
        path: '/forgot-password',
        builder: (context, state) {
          AuthBinding().dependencies();
          return const EnterEmailView(title: 'Forgot Password');
        },
      ),
      GoRoute(
        path: '/forgot-password/verify',
        builder: (context, state) {
          final email = state.extra as String;
          AuthBinding().dependencies();
          return VerifyOtpView(email: email);
        },
      ),
      GoRoute(
        path: '/forgot-password/reset',
        builder: (context, state) {
          final map = state.extra as Map<String, String>;
          AuthBinding().dependencies();
          return ResetPasswordView(email: map['email']!, otp: map['otp']!);
        },
      ),
      GoRoute(
        path: '/change-password',
        builder: (context, state) {
          AuthBinding().dependencies();
          return const EnterEmailView(title: 'Change Password');
        },
      ),
      GoRoute(path: '/home', builder: (context, state) => const HomeView()),
    ],
    initialLocation: '/login',
  );
}
