import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';

import '../controllers/auth_controller.dart';

class RegisterView extends StatefulWidget {
  const RegisterView({super.key});

  @override
  State<RegisterView> createState() => _RegisterViewState();
}

class _RegisterViewState extends State<RegisterView> {
  final _formKey = GlobalKey<FormState>();

  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  final List<String> _universities = [
    'University A',
    'University B',
    'University C',
  ];

  final List<String> _branches = [
    'Computer Science',
    'Mechanical',
    'Electrical',
    'Civil',
  ];

  final List<String> _semesters = List.generate(8, (i) => 'Semester ${i + 1}');

  String? _selectedUniversity;
  String? _selectedBranch;
  String? _selectedSemester;

  String? _requiredValidator(String? value, [String field = 'field']) {
    if (value == null || value.isEmpty) {
      return '$field is required';
    }
    return null;
  }

  String? _emailValidator(String? value) {
    if (value == null || value.isEmpty) return 'Email is required';
    final regex = RegExp(r'^\S+@\S+\.\S+$');
    if (!regex.hasMatch(value)) return 'Enter a valid email';
    return null;
  }

  String? _passwordValidator(String? value) {
    if (value == null || value.isEmpty) return 'Password is required';
    if (value.length < 6) return 'Password must be at least 6 characters';
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final authController = Get.find<AuthController>();

    return Scaffold(
      appBar: AppBar(title: const Text('Register')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: SingleChildScrollView(
            child: Column(
              children: [
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(labelText: 'Name'),
                  validator: (v) => _requiredValidator(v, 'Name'),
                ),
                TextFormField(
                  controller: _emailController,
                  decoration: const InputDecoration(labelText: 'Email'),
                  keyboardType: TextInputType.emailAddress,
                  validator: _emailValidator,
                ),
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(labelText: 'University'),
                  items: _universities
                      .map((u) => DropdownMenuItem(value: u, child: Text(u)))
                      .toList(),
                  value: _selectedUniversity,
                  onChanged: (val) => setState(() => _selectedUniversity = val),
                  validator: (v) => v == null ? 'University is required' : null,
                ),
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(labelText: 'Semester'),
                  items: _semesters
                      .map((s) => DropdownMenuItem(value: s, child: Text(s)))
                      .toList(),
                  value: _selectedSemester,
                  onChanged: (val) => setState(() => _selectedSemester = val),
                  validator: (v) => v == null ? 'Semester is required' : null,
                ),
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(labelText: 'Branch'),
                  items: _branches
                      .map((b) => DropdownMenuItem(value: b, child: Text(b)))
                      .toList(),
                  value: _selectedBranch,
                  onChanged: (val) => setState(() => _selectedBranch = val),
                  validator: (v) => v == null ? 'Branch is required' : null,
                ),
                TextFormField(
                  controller: _passwordController,
                  obscureText: true,
                  decoration: const InputDecoration(labelText: 'Password'),
                  validator: _passwordValidator,
                ),
                const SizedBox(height: 16),
                Obx(
                  () => authController.loading.value
                      ? const CircularProgressIndicator()
                      : ElevatedButton(
                          onPressed: () async {
                            if (_formKey.currentState!.validate()) {
                              await authController.register(
                                _nameController.text,
                                _emailController.text,
                                _passwordController.text,
                              );
                            }
                          },
                          child: const Text('Register'),
                        ),
                ),
                TextButton(
                  onPressed: () {
                    context.go('/login');
                  },
                  child: const Text('Have an account? Login'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
