import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:go_router/go_router.dart';
import 'package:somayya_academy/components/app_button.dart';
import 'package:somayya_academy/components/app_text_field.dart';

import '../controllers/auth_controller.dart';

class LoginView extends StatelessWidget {
  LoginView({super.key});

  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    final emailRegex = RegExp(r'^\S+@\S+\.\S+$');
    if (!emailRegex.hasMatch(value)) {
      return 'Enter a valid email';
    }
    return null;
  }

  String? _validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final authController = Get.find<AuthController>();
    return Scaffold(
      appBar: AppBar(title: const Text('Login')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Column(
            children: [
              AppTextField(
                controller: _emailController,
                labelText: 'Email',
                keyboardType: TextInputType.emailAddress,
                validator: _validateEmail,
              ),
              AppTextField(
                controller: _passwordController,
                labelText: 'Password',
                obscureText: true,
                validator: _validatePassword,
              ),
              const SizedBox(height: 16),
              Obx(
                () => AppButton(
                  text: 'Login',
                  isLoading: authController.loading.value,
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      // Show the reCAPTCHA page as a dialog

                      authController.login(
                        _emailController.text,
                        _passwordController.text,
                      );
                    }
                  },
                ),
              ),
              TextButton(
                onPressed: () {
                  context.push('/register');
                },
                child: const Text('No account? Register'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
