import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../widgets/otp_input.dart';

class VerifyOtpView extends StatelessWidget {
  const VerifyOtpView({super.key, required this.email});

  final String email;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Verify OTP')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text('Enter OTP sent to $email'),
            const SizedBox(height: 16),
            OtpInput(
              length: 6,
              onCompleted: (otp) {
                context.go(
                  '/forgot-password/reset',
                  extra: {'email': email, 'otp': otp},
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
