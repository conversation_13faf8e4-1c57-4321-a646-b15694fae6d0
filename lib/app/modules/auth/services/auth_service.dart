import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';

import '../models/user_model.dart';

class AuthService extends GetxService {
  final Dio _dio = Dio(BaseOptions(baseUrl: 'https://api.example.com'));
  final _storage = const FlutterSecureStorage();

  Future<User?> login(String email, String password) async {
    try {
      final response = await _dio.post(
        '/login',
        data: {'email': email, 'password': password},
      );
      final user = User.fromJson(response.data);
      await _storage.write(key: 'token', value: user.token);
      return user;
    } catch (e) {
      rethrow;
    }
  }

  Future<User?> register(String name, String email, String password) async {
    try {
      final response = await _dio.post(
        '/register',
        data: {'name': name, 'email': email, 'password': password},
      );
      final user = User.fromJson(response.data);
      await _storage.write(key: 'token', value: user.token);
      return user;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> requestOtp(String email) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      debugPrint('OTP requested for $email');
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> verifyOtp(String email, String otp) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      debugPrint('OTP verified for $email with otp $otp');
      return otp == '123456'; // Simulate successful OTP
    } catch (e) {
      rethrow;
    }
  }

  Future<User> registerWithPassword(
    String email,
    String otp,
    String password,
  ) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      return User(
        id: '123',
        name: 'John Doe',
        email: email,
        token: 'fake-token',
      );
    } catch (e) {
      rethrow;
    }
  }

  Future<User> updateProfile({
    required String name,
    required String university,
    required String semester,
    required String branch,
  }) async {
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      final user = User(
        id: '123',
        name: name,
        email: '<EMAIL>',
        token: 'fake-token',
      );
      await _storage.write(key: 'token', value: user.token);
      debugPrint('Profile updated for $name');
      return user;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> logout() async {
    await _storage.delete(key: 'token');
  }

  Future<String?> getToken() => _storage.read(key: 'token');
}
