import 'package:somayya_academy/app/constants/app_constants.dart';
import 'package:somayya_academy/app/constants/string_constants.dart';

class Validator {
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return StringConstants.emailRequired;
    }
    final emailRegex = RegExp(AppConstants.emailRegex);
    if (!emailRegex.hasMatch(value)) {
      return StringConstants.invalidEmail;
    }
    return null;
  }

  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return StringConstants.passwordRequired;
    }
    if (value.length < 6) {
      return StringConstants.passwordLength;
    }
    return null;
  }
}
