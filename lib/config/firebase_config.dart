import 'package:flutter/foundation.dart';
import 'app_config.dart';

/// Firebase configuration class that provides flavor-specific Firebase settings
class FirebaseConfig {
  /// Get the Firebase project ID based on the current flavor
  static String get projectId {
    switch (AppConfig.flavor) {
      case AppFlavor.dev:
        return 'somayya-academy-dev';
      case AppFlavor.prod:
        return 'somayya-academy-prod';
    }
  }

  /// Get the Firebase app ID based on the current flavor and platform
  static String get appId {
    switch (AppConfig.flavor) {
      case AppFlavor.dev:
        if (defaultTargetPlatform == TargetPlatform.iOS) {
          return '1:123456789:ios:abcdef123456789dev';
        } else {
          return '1:123456789:android:abcdef123456789dev';
        }
      case AppFlavor.prod:
        if (defaultTargetPlatform == TargetPlatform.iOS) {
          return '1:987654321:ios:fedcba987654321prod';
        } else {
          return '1:987654321:android:fedcba987654321prod';
        }
    }
  }

  /// Get the Firebase messaging sender ID based on the current flavor
  static String get messagingSenderId {
    switch (AppConfig.flavor) {
      case AppFlavor.dev:
        return '123456789';
      case AppFlavor.prod:
        return '987654321';
    }
  }

  /// Get the Firebase API key based on the current flavor and platform
  static String get apiKey {
    switch (AppConfig.flavor) {
      case AppFlavor.dev:
        if (defaultTargetPlatform == TargetPlatform.iOS) {
          return 'AIzaSyDev_iOS_API_Key_Here';
        } else {
          return 'AIzaSyDev_Android_API_Key_Here';
        }
      case AppFlavor.prod:
        if (defaultTargetPlatform == TargetPlatform.iOS) {
          return 'AIzaSyProd_iOS_API_Key_Here';
        } else {
          return 'AIzaSyProd_Android_API_Key_Here';
        }
    }
  }

  /// Get the Firebase storage bucket based on the current flavor
  static String get storageBucket {
    switch (AppConfig.flavor) {
      case AppFlavor.dev:
        return 'somayya-academy-dev.appspot.com';
      case AppFlavor.prod:
        return 'somayya-academy-prod.appspot.com';
    }
  }

  /// Get the Firebase Auth domain based on the current flavor
  static String get authDomain {
    switch (AppConfig.flavor) {
      case AppFlavor.dev:
        return 'somayya-academy-dev.firebaseapp.com';
      case AppFlavor.prod:
        return 'somayya-academy-prod.firebaseapp.com';
    }
  }

  /// Get the Firebase database URL based on the current flavor
  static String? get databaseURL {
    switch (AppConfig.flavor) {
      case AppFlavor.dev:
        return 'https://somayya-academy-dev-default-rtdb.firebaseio.com/';
      case AppFlavor.prod:
        return 'https://somayya-academy-prod-default-rtdb.firebaseio.com/';
    }
  }

  /// Print current Firebase configuration (useful for debugging)
  static void printConfig() {
    if (kDebugMode) {
      print('=== Firebase Configuration ===');
      print('Flavor: ${AppConfig.flavor.name}');
      print('Project ID: $projectId');
      print('App ID: $appId');
      print('Messaging Sender ID: $messagingSenderId');
      print('API Key: ${apiKey.substring(0, 10)}...');
      print('Storage Bucket: $storageBucket');
      print('Auth Domain: $authDomain');
      print('Database URL: $databaseURL');
      print('==============================');
    }
  }
}
