import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../config/app_config.dart';

class ApiService {
  static ApiService? _instance;
  late Dio _dio;

  ApiService._internal() {
    _dio = Dio();
    _setupInterceptors();
  }

  static ApiService get instance {
    _instance ??= ApiService._internal();
    return _instance!;
  }

  void _setupInterceptors() {
    _dio.options.baseUrl = AppConfig.apiBaseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);

    // Add logging interceptor for dev environment
    if (AppConfig.enableDetailedLogging) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        requestHeader: true,
        responseHeader: true,
        error: true,
        logPrint: (obj) {
          if (kDebugMode) {
            print('[API] $obj');
          }
        },
      ));
    }

    // Add request interceptor for common headers
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        // Add common headers
        options.headers['Content-Type'] = 'application/json';
        options.headers['Accept'] = 'application/json';
        
        // Add flavor-specific headers if needed
        if (AppConfig.isDev) {
          options.headers['X-Environment'] = 'development';
        }
        
        handler.next(options);
      },
      onError: (error, handler) {
        // Handle errors differently based on flavor
        if (AppConfig.enableDetailedLogging) {
          if (kDebugMode) {
            print('[API Error] ${error.message}');
            print('[API Error] ${error.response?.data}');
          }
        }
        
        handler.next(error);
      },
    ));
  }

  // Example API methods
  Future<Response> get(String path, {Map<String, dynamic>? queryParameters}) {
    return _dio.get(path, queryParameters: queryParameters);
  }

  Future<Response> post(String path, {dynamic data}) {
    return _dio.post(path, data: data);
  }

  Future<Response> put(String path, {dynamic data}) {
    return _dio.put(path, data: data);
  }

  Future<Response> delete(String path) {
    return _dio.delete(path);
  }

  // Example method showing flavor-specific behavior
  Future<Map<String, dynamic>> getAppInfo() async {
    try {
      final response = await get('/app/info');
      return response.data;
    } catch (e) {
      // In dev, return mock data if API fails
      if (AppConfig.isDev) {
        return {
          'version': '1.0.0-dev',
          'environment': 'development',
          'features': ['debug_mode', 'mock_data'],
        };
      }
      rethrow;
    }
  }
}
