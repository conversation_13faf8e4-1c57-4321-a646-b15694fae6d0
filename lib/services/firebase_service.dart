import 'package:flutter/foundation.dart';

import '../config/app_config.dart';
import '../config/firebase_config.dart';
// TODO: Uncomment these imports when Firebase is added to pubspec.yaml
// import 'package:firebase_core/firebase_core.dart';
// import '../config/firebase_options_dev.dart' as firebase_options_dev;
// import '../config/firebase_options_prod.dart' as firebase_options_prod;

/// Service class to handle Firebase initialization based on app flavors
class FirebaseService {
  static bool _initialized = false;

  /// Initialize Firebase with the appropriate configuration based on the current flavor
  static Future<void> initialize() async {
    if (_initialized) {
      if (kDebugMode) {
        print('Firebase already initialized');
      }
      return;
    }

    try {
      // Print configuration for debugging
      if (AppConfig.enableDetailedLogging) {
        FirebaseConfig.printConfig();
      }

      // Initialize Firebase based on flavor
      await _initializeForFlavor(AppConfig.flavor);

      _initialized = true;

      if (kDebugMode) {
        print(
          '✅ Firebase initialized successfully for ${AppConfig.flavor.name} flavor',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Firebase initialization failed: $e');
      }
      rethrow;
    }
  }

  /// Initialize Firebase for a specific flavor
  static Future<void> _initializeForFlavor(AppFlavor flavor) async {
    switch (flavor) {
      case AppFlavor.dev:
        await _initializeDevFirebase();
        break;
      case AppFlavor.prod:
        await _initializeProdFirebase();
        break;
    }
  }

  /// Initialize Firebase for development environment
  static Future<void> _initializeDevFirebase() async {
    // TODO: Uncomment when Firebase is added to pubspec.yaml
    /*
    await Firebase.initializeApp(
      options: firebase_options_dev.DefaultFirebaseOptions.currentPlatform,
    );
    */

    // For now, just log that we would initialize dev Firebase
    if (kDebugMode) {
      print('🔧 Dev Firebase configuration ready');
      print('   Project: ${FirebaseConfig.projectId}');
      print('   Storage: ${FirebaseConfig.storageBucket}');
    }
  }

  /// Initialize Firebase for production environment
  static Future<void> _initializeProdFirebase() async {
    // TODO: Uncomment when Firebase is added to pubspec.yaml
    /*
    await Firebase.initializeApp(
      options: firebase_options_prod.DefaultFirebaseOptions.currentPlatform,
    );
    */

    // For now, just log that we would initialize prod Firebase
    if (kDebugMode) {
      print('🚀 Prod Firebase configuration ready');
      print('   Project: ${FirebaseConfig.projectId}');
      print('   Storage: ${FirebaseConfig.storageBucket}');
    }
  }

  /// Check if Firebase has been initialized
  static bool get isInitialized => _initialized;

  /// Reset initialization state (useful for testing)
  static void reset() {
    _initialized = false;
  }

  /// Get Firebase configuration summary for current flavor
  static Map<String, dynamic> getConfigSummary() {
    return {
      'flavor': AppConfig.flavor.name,
      'projectId': FirebaseConfig.projectId,
      'appId': FirebaseConfig.appId,
      'messagingSenderId': FirebaseConfig.messagingSenderId,
      'storageBucket': FirebaseConfig.storageBucket,
      'authDomain': FirebaseConfig.authDomain,
      'databaseURL': FirebaseConfig.databaseURL,
      'initialized': _initialized,
    };
  }
}
