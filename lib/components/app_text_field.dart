import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AppText<PERSON>ield extends StatelessWidget {
  final TextEditingController? controller;
  final String labelText;
  final bool obscureText;
  final TextInputType? keyboardType;
  final String? Function(String?)? validator;

  const AppTextField({
    super.key,
    this.controller,
    required this.labelText,
    this.obscureText = false,
    this.keyboardType,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    final _obscureText = obscureText.obs;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Obx(
        () => TextForm<PERSON>ield(
          controller: controller,
          obscureText: _obscureText.value,
          keyboardType: keyboardType,
          decoration: InputDecoration(
            labelText: labelText,
            suffixIcon: obscureText
                ? IconButton(
                    onPressed: () {
                      _obscureText.value = !_obscureText.value;
                    },
                    icon: Icon(
                      _obscureText.value
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                  )
                : null,
          ),
          validator: validator,
          autovalidateMode: AutovalidateMode.onUserInteraction,
        ),
      ),
    );
  }
}
