import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  static const _primarySwatch = Colors.deepPurple;
  static const _secondaryColor = Colors.amber;
  static const _borderRadius = 8.0;
  static const _greyBorder = BorderSide(color: Colors.grey);
  static const _blueBorder = BorderSide(color: _primarySwatch, width: 2);
  static const _redBorder = BorderSide(color: Colors.red);

  static ThemeData _baseTheme(
    Brightness brightness,
    Color background,
    Color cardColor,
  ) {
    return ThemeData(
      useMaterial3: true,
      primarySwatch: _primarySwatch,
      visualDensity: VisualDensity.adaptivePlatformDensity,
      colorScheme: ColorScheme.fromSwatch(
        primarySwatch: _primarySwatch,
        brightness: brightness,
        backgroundColor: background,
        errorColor: Colors.red[400],
      ).copyWith(secondary: _secondaryColor),
      scaffoldBackgroundColor: background,
      textTheme: GoogleFonts.robotoTextTheme(
        TextTheme(
          displayLarge: TextStyle(
            fontSize: 72,
            fontWeight: FontWeight.bold,
            color: brightness == Brightness.light ? Colors.black : Colors.white,
          ),
          displayMedium: TextStyle(
            fontSize: 36,
            fontStyle: FontStyle.italic,
            color: brightness == Brightness.light
                ? Colors.black87
                : Colors.white70,
          ),
          displaySmall: TextStyle(
            fontSize: 14,
            color: brightness == Brightness.light
                ? Colors.black87
                : Colors.white70,
          ),
          headlineMedium: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.w600,
            color: brightness == Brightness.light ? Colors.black : Colors.white,
          ),
          bodyLarge: TextStyle(
            fontSize: 16,
            color: brightness == Brightness.light
                ? Colors.black87
                : Colors.white70,
          ),
          bodyMedium: TextStyle(
            fontSize: 14,
            color: brightness == Brightness.light
                ? Colors.black87
                : Colors.white70,
          ),
          bodySmall: TextStyle(
            fontSize: 12,
            color: brightness == Brightness.light
                ? Colors.black87
                : Colors.white70,
          ),
        ),
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: _primarySwatch,
        foregroundColor: Colors.white,
        elevation: 1,
        titleTextStyle: GoogleFonts.roboto(
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
        centerTitle: true,
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_borderRadius),
          borderSide: _greyBorder,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_borderRadius),
          borderSide: _blueBorder,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_borderRadius),
          borderSide: _greyBorder,
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_borderRadius),
          borderSide: _redBorder,
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_borderRadius),
          borderSide: BorderSide(color: Colors.red, width: 2),
        ),
        labelStyle: GoogleFonts.roboto(color: Colors.grey),
        hintStyle: GoogleFonts.roboto(color: Colors.grey),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          foregroundColor: Colors.white,
          backgroundColor: _primarySwatch,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_borderRadius),
          ),
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          textStyle: GoogleFonts.roboto(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: _primarySwatch,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_borderRadius),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: _primarySwatch,
          side: BorderSide(color: _primarySwatch, width: 1.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_borderRadius),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        ),
      ),
      cardTheme: CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        color: cardColor,
      ),
      dialogTheme: DialogThemeData(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        backgroundColor: cardColor,
        titleTextStyle: GoogleFonts.roboto(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: brightness == Brightness.light ? Colors.black87 : Colors.white,
        ),
        contentTextStyle: GoogleFonts.roboto(
          fontSize: 16,
          color: brightness == Brightness.light
              ? Colors.black54
              : Colors.white70,
        ),
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: _primarySwatch,
        foregroundColor: Colors.white,
        shape: CircleBorder(),
      ),
      dividerTheme: const DividerThemeData(
        color: Colors.grey,
        thickness: 1,
        space: 1,
      ),
      iconTheme: const IconThemeData(color: _primarySwatch, size: 24),
      chipTheme: ChipThemeData(
        backgroundColor: brightness == Brightness.light
            ? Colors.grey[200]
            : Colors.grey[800],
        selectedColor: Colors.blue[brightness == Brightness.light ? 100 : 800],
        secondarySelectedColor: _primarySwatch,
        labelStyle: GoogleFonts.roboto(
          color: brightness == Brightness.light
              ? Colors.black87
              : Colors.white70,
        ),
        secondaryLabelStyle: GoogleFonts.roboto(color: Colors.white),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_borderRadius),
        ),
      ),
      snackBarTheme: SnackBarThemeData(
        backgroundColor:
            Colors.blue[brightness == Brightness.light ? 800 : 900],
        contentTextStyle: GoogleFonts.roboto(color: Colors.white),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_borderRadius),
        ),
        behavior: SnackBarBehavior.floating,
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: brightness == Brightness.light
            ? Colors.white
            : Colors.grey,
        selectedItemColor: _primarySwatch,
        unselectedItemColor: brightness == Brightness.light
            ? Colors.grey
            : Colors.white70,
        selectedLabelStyle: GoogleFonts.roboto(fontWeight: FontWeight.w600),
      ),
    );
  }

  static ThemeData get light =>
      _baseTheme(Brightness.light, Colors.grey[100]!, Colors.white);

  static ThemeData get dark =>
      _baseTheme(Brightness.dark, Colors.grey[900]!, Colors.grey[850]!);
}
