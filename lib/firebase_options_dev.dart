// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options_dev.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAcMLdvq0Icbf1iTuiPRLicSch9QizBfOI',
    appId: '1:1066625703529:web:2846103727304a320e8cf8',
    messagingSenderId: '1066625703529',
    projectId: 'somayya-academy-dev',
    authDomain: 'somayya-academy-dev.firebaseapp.com',
    storageBucket: 'somayya-academy-dev.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAqrnPlluBVWD75jG7csgduvbBOv065sWU',
    appId: '1:1066625703529:android:0880e442dc980c650e8cf8',
    messagingSenderId: '1066625703529',
    projectId: 'somayya-academy-dev',
    storageBucket: 'somayya-academy-dev.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyA50wOmJCOkGHIEvRjPomhYBh8VVOCz9gU',
    appId: '1:1066625703529:ios:6073cc0e988c57240e8cf8',
    messagingSenderId: '1066625703529',
    projectId: 'somayya-academy-dev',
    storageBucket: 'somayya-academy-dev.firebasestorage.app',
    iosBundleId: 'com.somayyaacademy.dev',
  );
}
