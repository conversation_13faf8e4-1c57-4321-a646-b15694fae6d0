# Flutter Flavors Setup

This project is configured with Flutter flavors to support different environments: **dev** and **prod**.

## Available Flavors

### Dev Flavor
- **Package ID (Android)**: `com.somayyaacademy.dev`
- **Bundle ID (iOS)**: `com.somayyaacademy.dev`
- **App Name**: "Somayya Academy Dev"
- **API Base URL**: `https://dev-api.somayyaacademy.com`
- **Features**: Debug features enabled, detailed logging, no analytics/crash reporting

### Prod Flavor
- **Package ID (Android)**: `com.somayyaacademy`
- **Bundle ID (iOS)**: `com.somayyaacademy`
- **App Name**: "Somayya Academy"
- **API Base URL**: `https://api.somayyaacademy.com`
- **Features**: Production optimized, analytics and crash reporting enabled

## Running the App

### Command Line

#### Development Environment
```bash
# Debug mode
flutter run --flavor dev

# Release mode
flutter run --flavor dev --release

# Profile mode
flutter run --flavor dev --profile
```

#### Production Environment
```bash
# Debug mode
flutter run --flavor prod

# Release mode
flutter run --flavor prod --release

# Profile mode
flutter run --flavor prod --profile
```

### VSCode

Use the pre-configured launch configurations in `.vscode/launch.json`:

1. **Flutter (Dev)** - Run dev flavor in debug mode
2. **Flutter (Prod)** - Run prod flavor in debug mode
3. **Flutter (Dev - Release)** - Run dev flavor in release mode
4. **Flutter (Prod - Release)** - Run prod flavor in release mode

## Building the App

### Android

#### Development APK/AAB
```bash
# APK
flutter build apk --flavor dev

# App Bundle
flutter build appbundle --flavor dev
```

#### Production APK/AAB
```bash
# APK
flutter build apk --flavor prod

# App Bundle
flutter build appbundle --flavor prod
```

### iOS

#### Development IPA
```bash
flutter build ios --flavor dev --release
```

#### Production IPA
```bash
flutter build ios --flavor prod --release
```

## Using Flavors in Code

### Detecting Current Flavor

```dart
import 'package:somayya_academy/config/app_config.dart';

// Check current flavor
if (AppConfig.isDev) {
  print('Running in development mode');
} else if (AppConfig.isProd) {
  print('Running in production mode');
}

// Get flavor-specific configuration
String apiUrl = AppConfig.apiBaseUrl;
String appName = AppConfig.appName;
bool debugFeatures = AppConfig.enableDebugFeatures;
```

### Configuration Examples

The `AppConfig` class provides flavor-specific configurations:

- **API URLs**: Different endpoints for dev and prod
- **App Names**: Different display names
- **Feature Flags**: Enable/disable features per environment
- **Logging**: Detailed logging in dev, minimal in prod
- **Analytics**: Disabled in dev, enabled in prod
- **Database Names**: Separate databases per flavor

### Example Service Usage

```dart
import 'package:somayya_academy/services/api_service.dart';

// The API service automatically uses the correct base URL
final apiService = ApiService.instance;
final response = await apiService.get('/users');
```

## File Structure

```
lib/
├── config/
│   └── app_config.dart          # Flavor configuration
├── services/
│   └── api_service.dart         # Example service using flavors
└── main.dart                    # Updated to use flavor config

.vscode/
└── launch.json                  # VSCode launch configurations

android/app/
└── build.gradle.kts             # Android flavor configuration

ios/
├── Podfile                      # iOS build configurations
└── Runner.xcodeproj/
    └── project.pbxproj          # iOS flavor configurations
```

## Troubleshooting

### iOS Build Issues

If you encounter iOS build issues after adding flavors:

1. Clean the project:
   ```bash
   flutter clean
   cd ios && rm -rf Pods Podfile.lock && pod install
   ```

2. Regenerate iOS project files if needed:
   ```bash
   cd ios
   pod install
   ```

### Android Build Issues

If you encounter Android build issues:

1. Clean the project:
   ```bash
   flutter clean
   cd android && ./gradlew clean
   ```

2. Rebuild:
   ```bash
   flutter build apk --flavor dev
   ```

### Flavor Not Detected

If the app doesn't detect the correct flavor:

1. Make sure you're using the `--flavor` argument when running
2. Check that the `FLUTTER_APP_FLAVOR` environment variable is set correctly
3. The app will fall back to detecting based on debug/release mode

## Adding New Flavors

To add a new flavor (e.g., "staging"):

1. **Android**: Add new product flavor in `android/app/build.gradle.kts`
2. **iOS**: Add new build configurations in Xcode project
3. **Podfile**: Add new configurations to `ios/Podfile`
4. **Dart**: Add new enum value to `AppFlavor` in `app_config.dart`
5. **VSCode**: Add new launch configuration in `.vscode/launch.json`
